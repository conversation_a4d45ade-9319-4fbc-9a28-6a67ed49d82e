# Keyboard Repeat Detection System

## Overview

The keyboard repeat detection system provides continuous scrolling functionality for the DTXMania song selection stage. When a user holds down an arrow key, the system automatically generates repeat commands with accelerating frequency, similar to the original DTXManiaNX behavior.

## Features

1. **Key Hold Tracking**: Tracks when keys are pressed and held down
2. **Repeat Rate Acceleration**: Starts with slow repeat rate and accelerates to fast rate
3. **Input Command Queue**: Decouples input detection from command processing
4. **Configurable Settings**: All timing parameters are configurable via Config.ini

## Configuration

The keyboard repeat system uses hardcoded default values for optimal responsiveness:

- **Initial Delay**: 200ms (time between initial press and first repeat)
- **Final Delay**: 50ms (time between repeats at maximum speed)
- **Acceleration Time**: 1000ms (time to accelerate from initial to final rate)

These values provide a good balance between preventing accidental rapid navigation and allowing quick menu traversal when needed.

## Implementation Details

### Core Components

#### KeyRepeatState Class
Tracks the state of each key:
- `IsPressed`: Whether the key is currently held down
- `InitialPressTime`: When the key was first pressed
- `LastRepeatTime`: When the last repeat command was generated
- `CurrentRepeatInterval`: Current time between repeats
- `HasStartedRepeating`: Whether repeat mode has begun

#### InputCommand Structure
Represents a queued input command:
- `Type`: The type of command (MoveUp, MoveDown, etc.)
- `Timestamp`: When the command was generated
- `IsRepeat`: Whether this is a repeat command or initial press

#### InputCommandType Enum
Supported command types:
- `MoveUp`: Navigate to previous song
- `MoveDown`: Navigate to next song
- `MoveLeft`: Previous difficulty
- `MoveRight`: Next difficulty
- `Activate`: Select current item
- `Back`: Go back/cancel

### Processing Flow

1. **Input Detection**: Each frame, check keyboard state for tracked keys
2. **State Update**: Update KeyRepeatState for each key based on press/release
3. **Command Generation**: Generate InputCommand when key is pressed or repeat threshold is met
4. **Queue Processing**: Process all queued commands in order
5. **Command Execution**: Execute the appropriate action for each command

### Acceleration Algorithm

The repeat rate accelerates using linear interpolation:

```csharp
double accelerationProgress = Math.Min(1.0, timeSinceInitialPress / accelerationTime);
currentInterval = MathHelper.Lerp(initialDelay, finalDelay, accelerationProgress);
```

This provides smooth acceleration from slow to fast repeat rates.

## Usage

The system is automatically active in the SongSelectionStage. Users can:

1. **Single Press**: Tap arrow keys for single navigation steps
2. **Hold for Repeat**: Hold arrow keys for continuous scrolling
3. **Acceleration**: Longer holds result in faster scrolling

## Tracked Keys

The following keys support repeat detection:
- **Arrow Keys**: Up, Down, Left, Right (navigation)
- **Enter**: Activate/select (with repeat for rapid selection)
- **Escape**: Back/cancel (with repeat for rapid cancellation)

## Performance Considerations

- Minimal overhead: Only tracks keys that are currently pressed
- Efficient queue processing: Commands are processed in batches
- Memory efficient: KeyRepeatState objects are reused
- Frame-rate independent: Uses elapsed time for consistent behavior

## Testing

The system includes unit tests for:
- Configuration default values
- InputCommand creation and properties
- KeyRepeatState reset functionality
- InputCommandType enum completeness

## Future Enhancements

Potential improvements:
- Per-key configuration (different rates for different keys)
- Adaptive acceleration based on user behavior
- Integration with other input devices (gamepad, MIDI)
- Visual feedback for repeat mode activation
